<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>截图OCR测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .test-button {
            background-color: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background-color 0.3s;
        }
        .test-button:hover {
            background-color: #005a87;
        }
        .test-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .result {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            display: none;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            display: none;
        }
        code {
            background-color: #f1f1f1;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>截图OCR功能测试</h1>
        
        <div class="info">
            <strong>说明：</strong>此页面用于测试截图OCR功能。请确保在uTools环境中运行。
        </div>

        <div class="test-section">
            <h3>环境检查</h3>
            <p id="env-status">正在检查环境...</p>
            <button id="check-env-btn" class="test-button">重新检查环境</button>
        </div>

        <div class="test-section">
            <h3>功能测试</h3>
            <div class="warning">
                <strong>注意：</strong>点击下面的按钮将启动截图功能，截图完成后会自动跳转到OCR识别页面。
            </div>
            
            <button id="test-capture-btn" class="test-button" disabled>测试截图OCR</button>
            <button id="test-api-btn" class="test-button" disabled>测试API调用</button>
            
            <div id="test-result" class="result"></div>
            <div id="test-error" class="error"></div>
        </div>

        <div class="test-section">
            <h3>使用方法</h3>
            <p>在控制台中可以直接调用以下方法：</p>
            <ul>
                <li><code>window.ocrAPI.captureAndOCR()</code> - 通过API调用</li>
                <li><code>window.ocrPlugin.captureAndOCR()</code> - 直接调用插件方法</li>
                <li><code>captureAndOCR()</code> - 全局函数调用（如果已定义）</li>
            </ul>
        </div>
    </div>

    <script>
        // 环境检查函数
        function checkEnvironment() {
            const statusEl = document.getElementById('env-status');
            const captureBtn = document.getElementById('test-capture-btn');
            const apiBtn = document.getElementById('test-api-btn');
            
            let status = [];
            let canTest = true;

            // 检查uTools API
            if (typeof utools !== 'undefined') {
                status.push('✅ uTools API 可用');
                
                if (typeof utools.screenCapture === 'function') {
                    status.push('✅ screenCapture 方法可用');
                } else {
                    status.push('❌ screenCapture 方法不可用');
                    canTest = false;
                }
                
                if (typeof utools.redirect === 'function') {
                    status.push('✅ redirect 方法可用');
                } else {
                    status.push('❌ redirect 方法不可用');
                    canTest = false;
                }
            } else {
                status.push('❌ uTools API 不可用');
                canTest = false;
            }

            // 检查插件API
            if (typeof window.ocrAPI !== 'undefined') {
                status.push('✅ ocrAPI 可用');
                
                if (typeof window.ocrAPI.captureAndOCR === 'function') {
                    status.push('✅ ocrAPI.captureAndOCR 方法可用');
                } else {
                    status.push('❌ ocrAPI.captureAndOCR 方法不可用');
                }
            } else {
                status.push('❌ ocrAPI 不可用');
            }

            // 检查插件实例
            if (typeof window.ocrPlugin !== 'undefined') {
                status.push('✅ ocrPlugin 可用');
                
                if (typeof window.ocrPlugin.captureAndOCR === 'function') {
                    status.push('✅ ocrPlugin.captureAndOCR 方法可用');
                } else {
                    status.push('❌ ocrPlugin.captureAndOCR 方法不可用');
                }
            } else {
                status.push('❌ ocrPlugin 不可用');
            }

            statusEl.innerHTML = status.join('<br>');
            
            captureBtn.disabled = !canTest;
            apiBtn.disabled = !canTest || typeof window.ocrAPI === 'undefined';
        }

        // 测试截图OCR功能
        function testCaptureOCR() {
            const resultEl = document.getElementById('test-result');
            const errorEl = document.getElementById('test-error');
            
            resultEl.style.display = 'none';
            errorEl.style.display = 'none';

            try {
                if (typeof utools !== 'undefined' && typeof utools.screenCapture === 'function') {
                    resultEl.innerHTML = '正在启动截图功能...';
                    resultEl.style.display = 'block';
                    
                    utools.screenCapture((image) => {
                        if (image) {
                            resultEl.innerHTML = '截图成功！正在跳转到OCR识别...';
                            try {
                                // 使用功能代码进行跳转
                                utools.redirect('ocr-clipboard', image);
                                resultEl.innerHTML = '截图成功！已跳转到OCR识别功能';
                            } catch (error) {
                                errorEl.innerHTML = `跳转失败: ${error.message}`;
                                errorEl.style.display = 'block';
                                resultEl.style.display = 'none';
                            }
                        } else {
                            resultEl.innerHTML = '截图已取消或失败';
                        }
                    });
                } else {
                    throw new Error('uTools API 不可用');
                }
            } catch (error) {
                errorEl.innerHTML = `测试失败: ${error.message}`;
                errorEl.style.display = 'block';
            }
        }

        // 测试API调用
        function testAPICall() {
            const resultEl = document.getElementById('test-result');
            const errorEl = document.getElementById('test-error');
            
            resultEl.style.display = 'none';
            errorEl.style.display = 'none';

            try {
                if (window.ocrAPI && typeof window.ocrAPI.captureAndOCR === 'function') {
                    resultEl.innerHTML = '正在通过API调用截图功能...';
                    resultEl.style.display = 'block';
                    
                    window.ocrAPI.captureAndOCR();
                } else {
                    throw new Error('ocrAPI 不可用');
                }
            } catch (error) {
                errorEl.innerHTML = `API调用失败: ${error.message}`;
                errorEl.style.display = 'block';
            }
        }

        // 绑定事件
        document.getElementById('check-env-btn').addEventListener('click', checkEnvironment);
        document.getElementById('test-capture-btn').addEventListener('click', testCaptureOCR);
        document.getElementById('test-api-btn').addEventListener('click', testAPICall);

        // 页面加载时检查环境
        window.addEventListener('load', checkEnvironment);

        // 定义全局函数供控制台调用
        window.captureAndOCR = function() {
            if (window.ocrPlugin && typeof window.ocrPlugin.captureAndOCR === 'function') {
                return window.ocrPlugin.captureAndOCR();
            } else if (window.ocrAPI && typeof window.ocrAPI.captureAndOCR === 'function') {
                return window.ocrAPI.captureAndOCR();
            } else {
                console.error('截图OCR功能不可用');
            }
        };
    </script>
</body>
</html>
