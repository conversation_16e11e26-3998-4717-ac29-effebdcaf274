# 截图OCR功能使用说明

## 功能概述

截图OCR功能允许用户通过调用uTools的截图API进行屏幕截图，截图完成后直接在当前插件内进行OCR文字识别。

## 实现方式

直接在当前插件内处理截图数据，无需跳转到其他功能，提供更流畅的用户体验。

## 调用方法

### 1. 通过插件实例调用（推荐）

```javascript
// 直接调用插件的截图OCR方法
window.ocrPlugin.captureAndOCR();
```

### 2. 通过API接口调用

```javascript
// 通过暴露的API调用
window.ocrAPI.captureAndOCR();
```

### 3. 通过全局函数调用

```javascript
// 使用全局函数（在测试页面中定义）
captureAndOCR();
```

### 4. 直接使用uTools API

```javascript
// 手动调用uTools截图API
utools.screenCapture((image) => {
    if (image) {
        // 直接在当前插件内处理截图
        if (window.ocrPlugin && typeof window.ocrPlugin.handleImageInputFromCapture === 'function') {
            window.ocrPlugin.handleImageInputFromCapture(image);
        }
    }
});
```

## 工作流程

1. **调用截图API**: 使用 `utools.screenCapture()` 启动截图模式
2. **用户截图**: 用户在屏幕上选择要截图的区域
3. **获取图片**: 截图完成后获得base64格式的图片数据
4. **显示界面**: 自动显示主界面并确保窗口可见
5. **直接识别**: 在当前插件内直接调用 `performOCR()` 进行识别
6. **显示结果**: 识别结果直接显示在界面上

## 环境要求

- 必须在uTools环境中运行
- 需要uTools支持 `screenCapture` 和 `redirect` API
- 插件必须已正确加载和初始化

## 错误处理

- **API不可用**: 检查是否在uTools环境中运行
- **截图取消**: 用户按ESC或点击取消时，不会执行后续操作
- **跳转失败**: 如果 `utools.redirect()` 失败，会自动在当前插件内处理图片
- **插件未找到**: 确保使用正确的功能代码 `'ocr-clipboard'` 而不是标签名称

### 常见错误及解决方案

1. **"uTools API不可用"**
   - 原因：不在uTools环境中运行
   - 解决：确保在uTools中打开插件

2. **截图后无响应**
   - 原因：可能是权限问题或插件方法不可用
   - 解决：检查控制台错误信息，确保插件已正确初始化

3. **OCR识别失败**
   - 原因：图片格式问题或OCR服务配置错误
   - 解决：检查OCR服务配置，确保API密钥等设置正确

## 测试方法

1. 打开 `test-capture.html` 文件
2. 检查环境状态
3. 点击"测试截图OCR"按钮
4. 进行截图操作
5. 验证是否正确跳转到OCR识别页面

## 配置说明

在 `plugin.json` 中，确保有以下配置：

```json
{
  "code": "ocr-clipboard",
  "explain": "剪切板图片识别",
  "cmds": [
    {
      "type": "img",
      "label": "剪切板图片OCR识别"
    }
  ]
}
```

`utools.redirect()` 使用的标签必须与配置中的 `label` 字段完全一致。

## 注意事项

1. 截图功能会暂时隐藏当前窗口，让用户选择截图区域
2. 截图完成后会自动跳转，无需手动操作
3. 如果用户取消截图，不会执行任何后续操作
4. 图片数据会自动传递给OCR识别功能，无需额外处理

## 扩展用法

可以将此功能集成到其他场景中：

```javascript
// 在快捷键处理中使用
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.shiftKey && e.key === 'O') {
        window.ocrPlugin.captureAndOCR();
    }
});

// 在右键菜单中使用
contextMenu.addItem('截图识别', () => {
    window.ocrPlugin.captureAndOCR();
});

// 在其他插件中调用
if (window.ocrAPI) {
    window.ocrAPI.captureAndOCR();
}
```
