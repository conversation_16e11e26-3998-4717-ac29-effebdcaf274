// 截图OCR功能演示脚本
// 可以直接在浏览器控制台中运行

console.log('=== 截图OCR功能演示 ===');

// 检查环境
function checkEnvironment() {
    console.log('\n🔍 检查环境...');
    
    const checks = [
        {
            name: 'uTools API',
            test: () => typeof utools !== 'undefined',
            required: true
        },
        {
            name: 'screenCapture 方法',
            test: () => typeof utools !== 'undefined' && typeof utools.screenCapture === 'function',
            required: true
        },
        {
            name: 'OCR插件实例',
            test: () => typeof window.ocrPlugin !== 'undefined',
            required: true
        },
        {
            name: 'captureAndOCR 方法',
            test: () => window.ocrPlugin && typeof window.ocrPlugin.captureAndOCR === 'function',
            required: true
        },
        {
            name: 'handleImageInputFromCapture 方法',
            test: () => window.ocrPlugin && typeof window.ocrPlugin.handleImageInputFromCapture === 'function',
            required: false
        },
        {
            name: 'ocrAPI',
            test: () => typeof window.ocrAPI !== 'undefined',
            required: false
        }
    ];
    
    let allRequired = true;
    
    checks.forEach(check => {
        const result = check.test();
        const status = result ? '✅' : '❌';
        const required = check.required ? '(必需)' : '(可选)';
        console.log(`${status} ${check.name} ${required}`);
        
        if (check.required && !result) {
            allRequired = false;
        }
    });
    
    console.log(`\n📊 环境检查结果: ${allRequired ? '✅ 可以使用' : '❌ 环境不完整'}`);
    return allRequired;
}

// 测试截图OCR功能
function testCaptureOCR() {
    console.log('\n🚀 开始测试截图OCR功能...');
    
    if (!checkEnvironment()) {
        console.error('❌ 环境检查失败，无法进行测试');
        return;
    }
    
    try {
        console.log('📸 调用截图功能...');
        console.log('💡 提示：请在屏幕上选择要截图的区域');
        
        window.ocrPlugin.captureAndOCR();
        
        console.log('✅ 截图功能已启动，等待用户操作...');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
}

// 手动截图测试
function manualCaptureTest() {
    console.log('\n🔧 手动截图测试...');
    
    if (typeof utools === 'undefined' || typeof utools.screenCapture !== 'function') {
        console.error('❌ uTools API 不可用');
        return;
    }
    
    console.log('📸 启动手动截图...');
    
    utools.screenCapture((image) => {
        if (image) {
            console.log('✅ 截图成功！');
            console.log('📊 图片信息:');
            console.log(`   - 类型: ${typeof image}`);
            console.log(`   - 长度: ${image.length} 字符`);
            console.log(`   - 格式: ${image.substring(0, 50)}...`);
            
            // 尝试处理图片
            if (window.ocrPlugin && typeof window.ocrPlugin.handleImageInputFromCapture === 'function') {
                console.log('🔄 开始OCR识别...');
                window.ocrPlugin.handleImageInputFromCapture(image);
            } else {
                console.warn('⚠️ OCR处理方法不可用');
            }
        } else {
            console.log('ℹ️ 截图已取消或失败');
        }
    });
}

// 显示使用说明
function showUsage() {
    console.log('\n📖 使用说明:');
    console.log('1. checkEnvironment() - 检查环境是否支持');
    console.log('2. testCaptureOCR() - 测试完整的截图OCR功能');
    console.log('3. manualCaptureTest() - 手动测试截图功能');
    console.log('4. window.ocrPlugin.captureAndOCR() - 直接调用截图OCR');
    console.log('5. window.ocrAPI.captureAndOCR() - 通过API调用');
    console.log('\n💡 推荐使用: testCaptureOCR()');
}

// 导出函数到全局作用域
window.checkEnvironment = checkEnvironment;
window.testCaptureOCR = testCaptureOCR;
window.manualCaptureTest = manualCaptureTest;
window.showUsage = showUsage;

// 自动执行环境检查
checkEnvironment();
showUsage();

console.log('\n🎯 快速开始: 在控制台输入 testCaptureOCR() 开始测试');
